require("dotenv").config();
const express = require("express");
const axios = require("axios");
const cors = require("cors");
const jwt = require("jsonwebtoken");
const fs = require("fs-extra");
const path = require("path");
const settings = require("./settings");

const app = express();
app.use(express.json());
app.use(cors(settings));

// Configuration
const ACCESS_TOKEN_SECRET = process.env.ACCESS_TOKEN_SECRET;
const PROMPT_FILE = path.join(__dirname, "prompt.json");

// Default prompt
const DEFAULT_PROMPT = `الزبون : ( {USER_INPUT} ), لا ترحب , انت موظف في شركة البريق الدائم للشحن الدولي، والتي تقع في ليبيا بمنطقة النوفليين الموقع : https://www.google.com/maps/place/Royal+shopping+mall/@32.8212364,13.251782,15.5z/data=!4m6!3m5!1s0x13a893e6679a987d:0xe5783ab4ff82f155!8m2!3d32.8203407!4d13.247688!16s%2Fg%2F11n0bjpg1q?entry=ttu&g_ep=EgoyMDI0MTIwOC4wIKXMDSoASAFQAw%3D%3D. تقدم الشركة خدمات شحن متكاملة تشمل نقل البضائع، التخزين، والتخليص الجمركي، مع تخصص في الشحن البحري عبر ميناء طرابلس والشحن الجوي عبر مطار معيتيقة. تعمل كمساعد افتراضي على موقع الشركة، حيث تساعد العملاء في تتبع الشحنات، حساب تكاليف الشحن، وتقديم معلومات عن الخدمات. في حالة عدم معرفتك للإجابة، يجب إعطاء رقم هاتف المدير للدعم الفني وهو 090123456، السيد محمد. المستندات المطلوبة لتخليص البضائع هي كود الشحنة
 واسم العميل ، مدة الشحن البحري إلى ليبيا في الشحن الجوي هي من اسبوع الى 10 ايام والبحري من اسبوعان الى شهر، تكلفة الشحن الجوي اغلى البحري، إمكانية شحن المواد الخطرة غير متوفرة حاليا، وطريقة دفع تكاليف الشحن عن طريق البطاقة الائتمانية. الأسعار ليست ثابتة بسبب تغير الدولار، في حال عدم معرفتك للاجابة يرجى من العملاء الاتصال بالمدير للحصول على معلومات دقيقة. اسم المدير السيد محمد، ورقم هاتف المدير هو 090123456`;

// Helper functions
async function ensureFileExists(filePath, defaultContent) {
  try {
    await fs.access(filePath);
  } catch (error) {
    await fs.writeJson(filePath, defaultContent);
  }
}



async function readPrompt() {
  await ensureFileExists(PROMPT_FILE, { prompt: DEFAULT_PROMPT });
  const data = await fs.readJson(PROMPT_FILE);
  return data.prompt || DEFAULT_PROMPT;
}

async function writePrompt(prompt) {
  await fs.writeJson(PROMPT_FILE, { prompt });
}

// Middleware for token validation
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, ACCESS_TOKEN_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
}



// Prompt management endpoints
app.get("/prompt", authenticateToken, async (req, res) => {
  try {
    const prompt = await readPrompt();
    res.json({ prompt });
  } catch (error) {
    console.error("Error retrieving prompt:", error);
    res.status(500).json({ error: "Failed to retrieve prompt" });
  }
});

app.put("/prompt", authenticateToken, async (req, res) => {
  try {
    const { prompt } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: "Prompt is required" });
    }

    await writePrompt(prompt);
    res.json({ message: "Prompt updated successfully", prompt });
  } catch (error) {
    console.error("Error updating prompt:", error);
    res.status(500).json({ error: "Failed to update prompt" });
  }
});

// Chat endpoint
app.post("/chat", async (req, res) => {
  const { prompt } = req.body;

  if (!prompt) {
    return res.status(400).json({ error: "Prompt is required" });
  }

  try {
    const systemPrompt = await readPrompt();
    const finalPrompt = systemPrompt.replace('{USER_INPUT}', prompt);

    const response = await axios.post(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent",
      {
        contents: [
          {
            parts: [
              {
                text: finalPrompt,
              },
            ],
          },
        ],
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        params: {
          key: process.env.API_KEY,
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error(error.response ? error.response.data : error.message);
    res.status(500).json({ error: "Failed to generate content" });
  }
});

app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    message: "Server is running",
    timestamp: new Date().toISOString()
  });
});

app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: "Something went wrong!" });
});

app.use((req, res) => {
  res.status(404).json({ error: "Endpoint not found" });
});

const PORT = process.env.SERVER_Port || 3000;
app.listen(PORT, () => {
  console.log(`
🚀 Gimini Flash Backend Server is running on port ${PORT}
📡 Available endpoints:
   - POST /chat (public)
   - GET /prompt (protected - get prompt)
   - PUT /prompt (protected - update prompt)
   - GET /health (public - health check)

🔐 Protected endpoints require Authorization header: Bearer <token>
💡 Use tokens from your main backend for authentication
`);
});
